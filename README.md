# SIH Auto Buyer

This script uses the Steam Inventory Helper (SIH) API to automatically purchase a specified item when its price drops below a set threshold.

## Features

- Continuously monitors the price of a specified item on the SIH marketplace
- Automatically purchases the item at the lowest available price (not exceeding your maximum)
- Displays detailed price information including all available price points
- Plays a sound alert when an item is successfully purchased (at approximately 1% volume)
- Periodically checks if the API key is working and only proceeds when it's valid
- Provides detailed logging of all activities and purchase attempts
- Implements smart error handling with exponential backoff
- Limits daily purchases to avoid excessive spending
- Tracks order status and provides detailed information about purchases

## Requirements

- Python 3.6 or higher
- `requests` library

## Installation

1. Make sure you have Python installed on your system
2. Install the required packages:
   ```
   pip install requests
   ```

## SIH Setup

Before using this script, you need to set up a project on SIH:

1. Create an account on [SIH.app](https://sih.app/)
2. Create a project on [SIH.app projects page](https://sih.app/skinprovider#projects) and get your API key
3. Fund your SIH balance
4. Refill your project balance
4. Make sure your Steam account is properly configured for trading

## Configuration

The script comes pre-configured with multiple target items:
- Nova | Candy Apple (Factory New): max $0.30, quantity 5
- AWP | Safari Mesh (Field-Tested): max $1.20, quantity 5
- Fracture Case: max $0.25, quantity 5
- Danger Zone Case: max $1.20, quantity 5
- MAC-10 | Candy Apple (Factory New): max $0.60, quantity 5
- Glock-18 | Candy Apple (Factory New): max $2.40, quantity 5
- Fever Case: max $0.60, quantity 5
- Check interval: 5-120 seconds (adaptive)
- Steam ID: *****************
- Trade token: bAhEDBx_

You can override these settings using command-line arguments.

## Usage

Run the script with:

```
python sih_autobuy.py
```

### Command Line Arguments

The script supports several command line arguments:

```
python sih_autobuy.py --help
```

Available options:
- `--api-key KEY`: Your SIH API key (overrides the default)
- `--item NAME`: The item to buy (overrides the default "Danger Zone Case")
- `--max-price PRICE`: Maximum price to pay (default: 1.00)
- `--quantity NUMBER`: Number of items to buy (default: 10)
- `--interval SECONDS`: Price check interval in seconds (default: 120)
- `--api-check-interval SECONDS`: How often to check if API key is working (default: 300)
- `--steam-id ID`: Your Steam ID (overrides the default)
- `--trade-token TOKEN`: Your Steam trade token (overrides the default)
- `--app-id ID`: Steam app ID (default: 730 for CS:GO)
- `--no-sound`: Disable sound alerts when items are purchased
- `--no-progress-bar`: Disable the progress bar display during wait intervals
- `--no-adaptive-interval`: Disable adaptive interval (use fixed interval instead)
- `--log-file PATH`: Custom log file path (default: sih_autobuy.log)

Example:
```
python sih_autobuy.py --item "AK-47 | Redline (Field-Tested)" --max-price 15.50 --quantity 1 --interval 30
```

Or to use the default settings (multiple items with their configured prices and quantities):
```
python sih_autobuy.py
```

To buy 5 Danger Zone Cases with a maximum price of $0.85 each:
```
python sih_autobuy.py --quantity 5 --max-price 0.85
```

## How It Works

1. **API Key Validation**: The script checks if your SIH API key is valid
2. **Multi-Item Price Monitoring**: Once the API key is validated, it checks the prices of all target items simultaneously
3. **Price Analysis**: The script displays current prices and availability for each item
4. **Purchase Decision**: If any item's price is below its threshold, it attempts to purchase that item at the lowest available price
5. **Order Tracking**: After a purchase, it checks the order status and provides details
6. **Sound Alert**: Plays a very quiet sound alert when a purchase is successful
7. **Quantity Tracking**: Keeps track of how many of each item have been purchased
8. **Auto-Completion**: Automatically stops when all desired quantities have been purchased
9. **Continuous Monitoring**: Continues monitoring prices until all target quantities are reached

## Advanced Features

- **Adaptive Interval System**: Automatically adjusts check frequency (5-120 seconds) based on:
  - Market conditions (items near threshold prices)
  - Recent purchase activity
  - API performance and rate limiting
  - Error rates and response times
- **Multi-Item Monitoring**: Simultaneously monitors prices for multiple items
- **Individual Item Tracking**: Tracks purchase progress for each item separately
- **Batch Purchasing**: Attempts to buy all requested items at once when prices are favorable
- **Smart Priority**: Automatically prioritizes items that are below their threshold prices
- **Smart Purchase Tracking**: Prevents buying the same exact item by tracking recent purchase prices and detecting when new items become available
- **Purchase Verification**: Automatically verifies if purchases were successful by checking order status and confirming items are received
- **Rate Limit Protection**: Automatically detects and handles API rate limiting
- **Progress Bar**: Displays a visual progress bar during wait intervals
- **Balance Checking**: Verifies sufficient balance before attempting purchases
- **Exponential Backoff**: Automatically increases wait time after errors to avoid API rate limits
- **Daily Purchase Limits**: Limits the number of purchases per day to avoid excessive spending
- **Detailed Logging**: Provides comprehensive logs of all activities and API responses
- **Order Status Tracking**: Monitors the status of purchases and provides detailed information
- **Similar Item Detection**: If the exact item name isn't found, suggests similar items

## Logging

All activities are logged to:
- The console
- A file named `sih_autobuy.log` (or your custom log file)

## Important Notes

- Make sure you have sufficient balance in your SIH account before running the script
- Use the exact item name as it appears on the SIH marketplace
- The script will continue running until you stop it (Ctrl+C)
- If the API key is invalid, the script will wait and check again every 5 minutes
- Sound alerts are played at approximately 1% volume to be minimally intrusive
- The script always buys at the lowest available price, not exceeding your maximum price
- The maximum price you set is used as an upper limit, not the actual purchase price
- The script will automatically stop after purchasing the specified quantity of items
- If the price is above your maximum, it will keep checking until the price drops or the quantity is reached
- The script will exit if your balance is too low to make any purchases
- A simple countdown timer shows the wait time between price checks (can be disabled with `--no-progress-bar`)
- The script attempts to buy all requested items at once when possible
- The countdown stays on one line and updates in real-time

## Disclaimer

This script is provided as-is with no warranties. Use at your own risk. Make sure you understand how the SIH marketplace works before using this script with real money.
