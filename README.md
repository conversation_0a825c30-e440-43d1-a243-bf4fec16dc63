# SIH Auto Buyer

This script automatically monitors the price of a specified item on the Steam Inventory Helper (SIH) marketplace and purchases it when the price drops below a specified threshold.

## Features

- Continuously monitors the price of a specified item
- Automatically purchases the item when the price drops below your set threshold
- Plays a sound alert when an item is successfully purchased
- Logs all activities to both console and a log file
- Handles errors gracefully

## Requirements

- Python 3.6 or higher
- `requests` library

## Installation

1. Make sure you have Python installed on your system
2. Install the required packages:
   ```
   pip install requests
   ```

## Configuration

Before running the script, you need to configure the following parameters in the `sih_autobuy.py` file:

- `API_KEY`: Your SIH API key (already set to the provided key)
- `TARGET_ITEM`: The name of the item you want to buy (already set to "Danger Zone Case")
- `MAX_PRICE`: The maximum price you're willing to pay (already set to 1.05 USD)
- `CHECK_INTERVAL`: How often to check the price in seconds (default: 60 seconds)
- `STEAM_ID`: Your Steam ID (already set to <PERSON>'s Steam ID: 76561198028787403)
- `TRADE_TOKEN`: Your Steam trade token (you'll be prompted to enter this if not provided)

## How to Find Your Steam ID and Trade Token

1. **Steam ID**:
   - Go to your Steam profile
   - Your Steam ID is in the URL: `https://steamcommunity.com/profiles/[YOUR_STEAM_ID]`
   - Or you can use a service like [SteamID Finder](https://steamid.io/)

2. **Trade Token**:
   - Go to your Steam Trade URL page: [Steam Trade URL](https://steamcommunity.com/my/tradeoffers/privacy)
   - Your trade token is the part after `&token=` in your trade URL

## Usage

Run the script with:

```
python sih_autobuy.py
```

If you haven't provided your Steam ID and trade token in the script, you'll be prompted to enter them.

### Command Line Arguments

The script supports several command line arguments:

```
python sih_autobuy.py --help
```

Available options:
- `--test`: Run in test mode without making actual purchases
- `--interval SECONDS`: Set the check interval in seconds (default: 60)
- `--steam-id STEAM_ID`: Provide your Steam ID
- `--trade-token TOKEN`: Provide your Steam trade token
- `--max-price PRICE`: Set the maximum price to pay (default: 1.05)
- `--no-sound`: Disable sound alerts when items are purchased

Example:
```
python sih_autobuy.py --test --interval 30 --max-price 1.10
```

The script will then:
1. Connect to the SIH API (unless in test mode)
2. Check your account balance (unless in test mode)
3. Start monitoring the price of the specified item
4. Purchase the item automatically when the price drops below your threshold

## Logging

All activities are logged to:
- The console
- A file named `sih_autobuy.log` in the same directory as the script

## Important Notes

- Make sure you have sufficient balance in your SIH account before running the script
- The script will continue running until you stop it (Ctrl+C) or an unrecoverable error occurs
- By default, the script will continue monitoring prices even after a successful purchase
- If the API key is invalid, the script will automatically switch to test mode
- Use the `--test` flag to run the script in test mode without making actual purchases

## Disclaimer

This script is provided as-is with no warranties. Use at your own risk. Make sure you understand how the SIH marketplace works before using this script with real money.
