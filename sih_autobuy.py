import requests
import time
import logging
import os
import winsound  # For Windows sound alerts
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("sih_autobuy.log"),
        logging.StreamHandler()
    ]
)

class SIHAutoBuyer:
    def __init__(self, api_key, target_item, max_price, check_interval=60, app_id=730, test_mode=False, sound_alert=True):
        """
        Initialize the SIH Auto Buyer.

        Args:
            api_key (str): Your SIH API key
            target_item (str): The name of the item to buy
            max_price (float): Maximum price to pay for the item in USD
            check_interval (int): How often to check prices in seconds
            app_id (int): The Steam app ID (730 for CS:GO)
            test_mode (bool): If True, run in test mode without actual purchases
            sound_alert (bool): If True, play a sound alert when an item is purchased
        """
        self.api_key = api_key
        self.target_item = target_item
        self.max_price = max_price
        self.check_interval = check_interval
        self.app_id = app_id
        self.test_mode = test_mode
        self.sound_alert = sound_alert
        self.base_url = "https://api.sih.market/api/v1"
        self.headers = {
            "apikey": self.api_key
        }

        # Verify API key and get project info
        if not self.test_mode:
            self.project_info = self._get_project_info()
            if self.project_info:
                logging.info(f"Connected to SIH project: {self.project_info['name']}")
                logging.info(f"Current balance: ${self.project_info['balance']}")
            else:
                logging.error("Failed to connect to SIH API. Check your API key.")
                logging.warning("Continuing in simulation mode. No actual purchases will be made.")
                self.test_mode = True
        else:
            logging.info("Running in test mode. No actual purchases will be made.")
            self.project_info = None

    def _get_project_info(self):
        """Get information about the current project."""
        try:
            response = requests.get(
                f"{self.base_url}/project",
                headers=self.headers
            )
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    return data.get("project")
            logging.error(f"Failed to get project info: {response.text}")
            return None
        except Exception as e:
            logging.error(f"Error getting project info: {str(e)}")
            return None

    def get_item_price(self):
        """Get the current price of the target item."""
        # If in test mode, simulate price data
        if self.test_mode:
            # Simulate a price that fluctuates around the target price
            import random
            simulated_price = round(self.max_price + random.uniform(-0.1, 0.1), 2)
            simulated_count = random.randint(1, 10)
            logging.info(f"[TEST MODE] Simulated price for {self.target_item}: ${simulated_price}")
            return simulated_price, simulated_count

        try:
            response = requests.get(
                f"{self.base_url}/get-min-item",
                headers=self.headers,
                params={
                    "item": self.target_item,
                    "appId": self.app_id
                }
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("success") and data.get("items"):
                    item_data = data["items"].get(self.target_item)
                    if item_data:
                        return item_data.get("price"), item_data.get("count")

            logging.error(f"Failed to get item price: {response.text}")
            return None, 0
        except Exception as e:
            logging.error(f"Error getting item price: {str(e)}")
            return None, 0

    def buy_item(self, steam_id, trade_token):
        """
        Buy the target item.

        Args:
            steam_id (str): Your Steam ID
            trade_token (str): Your Steam trade token

        Returns:
            bool: True if purchase was successful, False otherwise
        """
        try:
            # If in test mode, simulate a successful purchase
            if self.test_mode:
                logging.info(f"[TEST MODE] Would have purchased {self.target_item} for ${self.max_price}")
                return True

            # Generate a custom ID based on timestamp
            custom_id = f"auto_buy_{int(time.time())}"

            # Create the order
            response = requests.post(
                f"{self.base_url}/create-order",
                headers=self.headers,
                json={
                    "steamId": steam_id,
                    "token": trade_token,
                    "amount": self.max_price,
                    "item": self.target_item,
                    "customId": custom_id,
                    "test": False,  # Set to True for testing without actual purchase
                    "appId": self.app_id
                }
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    logging.info(f"Purchase successful! Order ID: {data.get('id')}")
                    logging.info(f"Remaining balance: ${data.get('balance')}")
                    return True
                else:
                    logging.error(f"Purchase failed: {data.get('error')}")
            else:
                logging.error(f"Purchase request failed: {response.text}")

            return False
        except Exception as e:
            logging.error(f"Error buying item: {str(e)}")
            return False

    def _play_alert_sound(self):
        """Play a sound alert when an item is purchased."""
        if not self.sound_alert:
            return

        try:
            # Unfortunately, winsound.Beep doesn't support volume control directly
            # We can use a lower frequency and shorter duration for a more subtle alert
            # Play a softer beep sound (lower frequency, shorter duration)
            winsound.Beep(500, 300)  # Lower frequency and shorter duration for quieter sound
            time.sleep(0.2)
            winsound.Beep(700, 300)  # Lower frequency and shorter duration for quieter sound
            logging.info("Sound alert played (reduced volume)")
        except Exception as e:
            logging.error(f"Failed to play sound alert: {str(e)}")

    def run(self, steam_id, trade_token):
        """
        Run the auto-buyer continuously.

        Args:
            steam_id (str): Your Steam ID
            trade_token (str): Your Steam trade token
        """
        logging.info(f"Starting auto-buyer for {self.target_item}")
        logging.info(f"Will buy when price is below ${self.max_price}")

        while True:
            try:
                current_price, available_count = self.get_item_price()

                if current_price is not None:
                    logging.info(f"Current price for {self.target_item}: ${current_price} (Available: {available_count})")

                    if current_price <= self.max_price and available_count > 0:
                        logging.info(f"Price is below threshold! Attempting to buy {self.target_item}")
                        success = self.buy_item(steam_id, trade_token)

                        if success:
                            logging.info("Purchase completed successfully!")
                            # Play sound alert for successful purchase
                            self._play_alert_sound()
                            # You can choose to exit after successful purchase or continue monitoring
                            # break
                    else:
                        if current_price > self.max_price:
                            logging.info(f"Price is above threshold (${self.max_price}). Waiting...")
                        elif available_count <= 0:
                            logging.info("No items available. Waiting...")

                # Wait before checking again
                time.sleep(self.check_interval)

            except KeyboardInterrupt:
                logging.info("Auto-buyer stopped by user.")
                break
            except Exception as e:
                logging.error(f"Error in auto-buyer loop: {str(e)}")
                # Wait a bit longer if there was an error
                time.sleep(self.check_interval * 2)


if __name__ == "__main__":
    import argparse

    # Set up command line arguments
    parser = argparse.ArgumentParser(description='SIH Auto Buyer - Automatically buy items when price drops below threshold')
    parser.add_argument('--test', action='store_true', help='Run in test mode without making actual purchases')
    parser.add_argument('--interval', type=int, default=60, help='Check interval in seconds (default: 60)')
    parser.add_argument('--steam-id', type=str, help='Your Steam ID')
    parser.add_argument('--trade-token', type=str, help='Your Steam trade token')
    parser.add_argument('--max-price', type=float, default=1.05, help='Maximum price to pay (default: 1.05)')
    parser.add_argument('--no-sound', action='store_true', help='Disable sound alerts on purchase')
    args = parser.parse_args()

    # Configuration
    API_KEY = "iehs6hVfrAF69vAipUuSfNI7k7qPorbYDzHowyf9"
    TARGET_ITEM = "Danger Zone Case"
    MAX_PRICE = args.max_price
    CHECK_INTERVAL = args.interval
    TEST_MODE = args.test
    SOUND_ALERT = not args.no_sound

    # Steam ID and trade token
    STEAM_ID = args.steam_id or "76561198028787403"  # Pavel's Steam ID
    TRADE_TOKEN = args.trade_token or "bAhEDBx_"  # Pavel's trade token

    # Create and run the auto-buyer
    auto_buyer = SIHAutoBuyer(API_KEY, TARGET_ITEM, MAX_PRICE, CHECK_INTERVAL, test_mode=TEST_MODE, sound_alert=SOUND_ALERT)
    auto_buyer.run(STEAM_ID, TRADE_TOKEN)
