import requests
import time
import logging
import os
import json
import sys
import winsound  # For Windows sound alerts
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("sih_autobuy.log"),
        logging.StreamHandler()
    ]
)

def wait_with_progress_bar(seconds, description="Waiting"):
    """
    Wait for the specified number of seconds with a very simple progress bar.

    Args:
        seconds (int): Number of seconds to wait
        description (str): Description to show in the progress bar
    """
    try:
        # Create a very simple progress bar that stays on one line
        for i in range(seconds, 0, -1):
            # Use a simple countdown format
            sys.stdout.write(f'\r{description} {i} seconds remaining...   ')
            sys.stdout.flush()
            time.sleep(1)

        # Complete the progress bar
        sys.stdout.write(f'\r{description} complete!                      \n')
        sys.stdout.flush()
    except Exception as e:
        # If there's an error with the progress bar, fall back to simple waiting
        logging.warning(f"Progress bar error: {str(e)}. Falling back to simple waiting.")
        logging.info(f"Waiting for {seconds} seconds...")
        time.sleep(seconds)

class SIHAutoBuyer:
    def __init__(self, api_key, target_items=None, check_interval=120, app_id=730, sound_alert=True,
                 api_check_interval=300, show_progress_bar=True, adaptive_interval=True):
        """
        Initialize the SIH Auto Buyer.

        Args:
            api_key (str): Your SIH API key
            target_items (list): List of dictionaries with item details [{"name": str, "max_price": float, "quantity": int, "bought": int}]
            check_interval (int): Base check interval in seconds (used as max when adaptive)
            app_id (int): The Steam app ID (730 for CS:GO)
            sound_alert (bool): If True, play a sound alert when an item is purchased
            api_check_interval (int): How often to check if API key is working (in seconds)
            show_progress_bar (bool): If True, display a progress bar during wait intervals
            adaptive_interval (bool): If True, use adaptive interval based on market conditions
        """
        self.api_key = api_key
        self.target_items = target_items or [
            {"name": "Nova | Candy Apple (Factory New)", "max_price": 0.3, "quantity": 5, "bought": 0},
            {"name": "AWP | Safari Mesh (Field-Tested)", "max_price": 0.12, "quantity": 5, "bought": 0},
            {"name": "Fracture Case", "max_price": 0.25, "quantity": 5, "bought": 0},
            {"name": "Danger Zone Case", "max_price": 1.2, "quantity": 5, "bought": 0},
            {"name": "MAC-10 | Candy Apple (Factory New)", "max_price": 0.6, "quantity": 5, "bought": 0},
            {"name": "Glock-18 | Candy Apple (Factory New)", "max_price": 2.4, "quantity": 5, "bought": 0},
            {"name": "Revolution Case", "max_price": 0.6, "quantity": 5, "bought": 0}
        ]
        self.base_check_interval = check_interval
        self.current_interval = 5 if adaptive_interval else check_interval  # Start aggressive
        self.adaptive_interval = adaptive_interval
        self.app_id = app_id
        self.sound_alert = sound_alert
        self.api_check_interval = api_check_interval
        self.show_progress_bar = show_progress_bar
        self.last_api_check = datetime.now() - timedelta(seconds=api_check_interval)  # Force immediate check
        self.base_url = "https://api.sih.market/api/v1"
        self.headers = {
            "apikey": self.api_key
        }
        self.project_info = None

        # Adaptive interval settings
        self.min_interval = 5      # Most aggressive (5 seconds)
        self.max_interval = check_interval  # Conservative fallback
        self.rate_limit_penalty = 30  # Add 30 seconds if rate limited
        self.consecutive_errors = 0
        self.last_purchase_time = None
        self.api_response_times = []  # Track API response times

        # Smart purchase tracking to prevent buying same item multiple times
        self.recent_purchases = {}  # {item_name: {'price': float, 'time': datetime, 'count': int}}
        self.purchase_memory_duration = 60  # Remember purchases for 60 seconds

        # Order tracking for purchase verification
        self.pending_orders = {}  # {custom_id: {'item_name': str, 'price': float, 'time': datetime, 'verified': bool}}
        self.order_check_interval = 10  # Check order status every 10 seconds

    def _calculate_adaptive_interval(self, items_near_threshold=0, recent_purchases=False, api_errors=0):
        """
        Calculate the next check interval based on market conditions and API performance.

        Args:
            items_near_threshold (int): Number of items close to their threshold prices
            recent_purchases (bool): Whether purchases were made recently
            api_errors (int): Number of consecutive API errors

        Returns:
            int: Next check interval in seconds
        """
        if not self.adaptive_interval:
            return self.base_check_interval

        # Start with base aggressive interval
        interval = self.min_interval

        # Increase interval based on consecutive errors (rate limiting protection)
        if api_errors > 0:
            # Exponential backoff for errors
            error_penalty = min(self.rate_limit_penalty * (2 ** (api_errors - 1)), 300)  # Max 5 minutes
            interval += error_penalty
            logging.info(f"Adding {error_penalty}s penalty due to {api_errors} consecutive API errors")

        # Decrease interval if items are near threshold (more aggressive monitoring)
        if items_near_threshold > 0:
            # More items near threshold = more aggressive checking
            threshold_bonus = max(0, 10 - (items_near_threshold * 2))
            interval = max(self.min_interval, interval - threshold_bonus)
            logging.debug(f"Items near threshold: {items_near_threshold}, reducing interval by {threshold_bonus}s")

        # Increase interval if recent purchases (give market time to update)
        if recent_purchases:
            interval += 15  # Add 15 seconds after purchases
            logging.debug("Recent purchases detected, adding 15s cooldown")

        # Keep within bounds
        interval = max(self.min_interval, min(self.max_interval, interval))

        # Track API response times for performance adjustment
        if len(self.api_response_times) > 10:
            avg_response_time = sum(self.api_response_times[-10:]) / 10
            if avg_response_time > 2.0:  # If API is slow, be less aggressive
                interval += 5
                logging.debug(f"Slow API responses ({avg_response_time:.1f}s avg), adding 5s to interval")

        return int(interval)

    def _should_skip_purchase(self, item_name, current_price):
        """
        Check if we should skip purchasing this item based on recent purchase history.

        Args:
            item_name (str): Name of the item to check
            current_price (float): Current lowest price of the item

        Returns:
            tuple: (should_skip: bool, reason: str)
        """
        if item_name not in self.recent_purchases:
            return False, "No recent purchases"

        purchase_info = self.recent_purchases[item_name]
        time_since_purchase = (datetime.now() - purchase_info['time']).total_seconds()

        # Clean up old purchase records
        if time_since_purchase > self.purchase_memory_duration:
            del self.recent_purchases[item_name]
            return False, "Purchase memory expired"

        last_price = purchase_info['price']

        # If the current price is the same as what we just bought, it's likely the same item
        if abs(current_price - last_price) < 0.001:  # Within 0.1 cent
            remaining_time = self.purchase_memory_duration - time_since_purchase
            return True, f"Same price (${current_price}) as recent purchase, likely same item (expires in {remaining_time:.0f}s)"

        # If the price increased, we successfully bought the cheapest one - can buy the next cheapest
        if current_price > last_price:
            logging.info(f"{item_name}: Price increased from ${last_price} to ${current_price} - previous purchase successful, can buy next cheapest")
            return False, "Price increased - can buy next cheapest item"

        # If price decreased, there's a new cheaper item available
        if current_price < last_price:
            logging.info(f"{item_name}: Price decreased from ${last_price} to ${current_price} - new cheaper item available!")
            return False, "Price decreased - new cheaper item available"

        return False, "Price analysis complete"

    def _check_order_status(self, custom_id):
        """
        Check the status of a specific order using SIH API.

        Args:
            custom_id (str): The custom ID of the order to check

        Returns:
            dict: Order information or None if failed
        """
        try:
            response = requests.get(
                f"{self.base_url}/get-order",
                headers=self.headers,
                params={"customId": custom_id},
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("success") and data.get("order"):
                    return data["order"]
                else:
                    logging.warning(f"Order check failed for {custom_id}: {data.get('error', 'Unknown error')}")
                    return None
            else:
                logging.warning(f"Order check HTTP error {response.status_code} for {custom_id}")
                return None

        except Exception as e:
            logging.error(f"Exception checking order {custom_id}: {e}")
            return None

    def _verify_pending_orders(self):
        """
        Check the status of all pending orders and update purchase tracking accordingly.
        """
        if not self.pending_orders:
            return

        orders_to_remove = []
        current_time = datetime.now()

        for custom_id, order_info in self.pending_orders.items():
            # Skip recently created orders (give them time to process)
            if (current_time - order_info['time']).total_seconds() < 5:
                continue

            # Check order status
            order_status = self._check_order_status(custom_id)

            if order_status:
                status = order_status.get('status', 'unknown')
                item_name = order_info['item_name']
                price = order_info['price']

                if status == 'finished':
                    logging.info(f"[SUCCESS] PURCHASE CONFIRMED: {item_name} at ${price} - Item received in inventory!")
                    order_info['verified'] = True
                    orders_to_remove.append(custom_id)

                elif status == 'failed' or status == 'penalized':
                    error_msg = order_status.get('error', 'Unknown error')
                    logging.warning(f"[FAILED] PURCHASE FAILED: {item_name} at ${price} - {error_msg}")

                    # Remove from recent purchases since it failed
                    if item_name in self.recent_purchases:
                        self.recent_purchases[item_name]['count'] -= 1
                        if self.recent_purchases[item_name]['count'] <= 0:
                            del self.recent_purchases[item_name]

                    orders_to_remove.append(custom_id)

                elif status in ['created', 'processing', 'sent']:
                    logging.debug(f"[PENDING] Order {custom_id} for {item_name} still {status}")

                else:
                    logging.warning(f"Unknown order status '{status}' for {custom_id}")

            # Remove old orders (older than 5 minutes)
            elif (current_time - order_info['time']).total_seconds() > 300:
                logging.warning(f"Order {custom_id} for {order_info['item_name']} timed out - removing from tracking")
                orders_to_remove.append(custom_id)

        # Clean up completed/failed orders
        for custom_id in orders_to_remove:
            del self.pending_orders[custom_id]

    def _check_api_key(self):
        """Check if the API key is working and update test_mode accordingly."""
        # Check if it's time to check the API key
        if (datetime.now() - self.last_api_check).total_seconds() < self.api_check_interval:
            # Not time to check API key yet
            return True  # Assume API key is still valid if we checked recently

        self.last_api_check = datetime.now()

        # Check if the API key is working
        logging.info(f"Checking if API key is working...")

        # Try up to 3 times to get project info
        max_retries = 3
        for attempt in range(1, max_retries + 1):
            try:
                self.project_info = self._get_project_info()

                if self.project_info:
                    logging.info(f"API key is working! Connected to SIH project: {self.project_info['name']}")
                    logging.info(f"Current balance: ${float(self.project_info['balance']):.2f}")
                    return True
                else:
                    if attempt < max_retries:
                        logging.warning(f"API key validation failed (attempt {attempt}/{max_retries}). Retrying...")
                        time.sleep(2)  # Short delay before retry
                    else:
                        logging.warning(f"API key is not working after {max_retries} attempts. Will check again in {self.api_check_interval} seconds.")
            except Exception as e:
                if attempt < max_retries:
                    logging.warning(f"Error checking API key (attempt {attempt}/{max_retries}): {str(e)}. Retrying...")
                    time.sleep(2)  # Short delay before retry
                else:
                    logging.error(f"Error checking API key after {max_retries} attempts: {str(e)}")

        return False

    def _get_project_info(self):
        """Get information about the current project."""
        try:
            response = requests.get(
                f"{self.base_url}/project",
                headers=self.headers
            )
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    return data.get("project")
            logging.error(f"Failed to get project info: {response.text}")
            return None
        except Exception as e:
            logging.error(f"Error getting project info: {str(e)}")
            return None

    def get_item_price(self, item_name=None):
        """
        Get the current price of the specified item using SIH API.

        Args:
            item_name (str, optional): Name of the item to check. If None, uses the first target item.

        Returns:
            tuple: (price, count) if successful, (None, 0) otherwise
        """
        start_time = time.time()
        try:
            # Use the provided item name or fall back to the first target item
            target_item = item_name or (self.target_items[0]['name'] if self.target_items else "")

            # First try to get the minimum price for the specific item
            response = requests.get(
                f"{self.base_url}/get-min-item",
                headers=self.headers,
                params={
                    "item": target_item,
                    "appId": self.app_id
                },
                timeout=10  # Add timeout for better error handling
            )

            # Track API response time
            response_time = time.time() - start_time
            self.api_response_times.append(response_time)
            if len(self.api_response_times) > 20:  # Keep only last 20 response times
                self.api_response_times.pop(0)

            # Check for rate limiting
            if response.status_code == 429:  # Too Many Requests
                logging.warning("Rate limit detected! API returned 429 status code - adding delay")
                self.consecutive_errors += 1
                time.sleep(2)  # Wait 2 seconds before continuing
                return None, 0
            elif response.status_code == 503:  # Service Unavailable
                logging.warning("API service unavailable! Status code 503 - adding delay")
                self.consecutive_errors += 1
                time.sleep(1)  # Wait 1 second before continuing
                return None, 0

            if response.status_code == 200:
                data = response.json()
                if data.get("success") and data.get("items"):
                    item_data = data["items"].get(target_item)
                    if item_data:
                        price = item_data.get("price")
                        count = item_data.get("count", 0)



                        # Get detailed price information if available
                        detailed_prices = []
                        if "detailed" in item_data:
                            detailed = item_data.get("detailed", [])
                            for price_info in detailed[:5]:  # Show up to 5 lowest prices
                                price_val = price_info.get("price", "N/A")
                                count_val = price_info.get("count", 0)
                                detailed_prices.append(f"${price_val} (x{count_val})")

                            if detailed_prices:
                                logging.info(f"Available prices: {', '.join(detailed_prices)}")

                        return price, count
                    else:
                        logging.warning(f"Item '{target_item}' not found in response")
                        logging.debug(f"Available items in response: {list(data.get('items', {}).keys())[:5]}")
                else:
                    error_msg = data.get("error", "Unknown error")
                    logging.error(f"API returned error for '{target_item}': {error_msg}")
                    if "not found" in error_msg.lower() or "invalid" in error_msg.lower():
                        logging.warning(f"Item name '{target_item}' might be incorrect or changed after CS2 update")
                    elif error_msg == "Unknown error":
                        logging.warning(f"API returned 'Unknown error' for '{target_item}' - item might not be available on SIH market")
            else:
                logging.error(f"Failed to get item price for '{target_item}'. Status code: {response.status_code}")
                logging.debug(f"Response: {response.text}")

            # If we couldn't get the specific item price, try getting all items
            # This might help diagnose if the item name is correct
            try:
                logging.debug("Attempting to get all available items for diagnostics...")
                all_items_response = requests.get(
                    f"{self.base_url}/get-items",
                    headers=self.headers,
                    params={"appId": self.app_id, "minified": True},
                    timeout=10
                )

                if all_items_response.status_code == 200:
                    all_items_data = all_items_response.json()
                    if all_items_data.get("success") and all_items_data.get("items"):
                        item_count = len(all_items_data["items"])
                        logging.debug(f"Found {item_count} items available on SIH market")

                        # Check if our target item is in the list with a different name
                        similar_items = [item for item in all_items_data["items"].keys()
                                        if target_item.lower() in item.lower()]

                        if similar_items:
                            logging.info(f"Found similar items: {', '.join(similar_items[:5])}")
                            logging.info(f"Consider using one of these exact names instead")
                    else:
                        logging.debug("Could not retrieve item list from API response")
                elif all_items_response.status_code == 400:
                    logging.debug(f"API endpoint /get-items returned 400 - possibly changed after CS2 update")
                    logging.debug(f"Response: {all_items_response.text[:200]}...")
                else:
                    logging.debug(f"Failed to get all items. Status: {all_items_response.status_code}")
            except Exception as e:
                logging.debug(f"Error while trying to get all items: {str(e)}")

            return None, 0
        except Exception as e:
            logging.error(f"Error getting item price: {str(e)}")
            return None, 0

    def buy_item(self, steam_id, trade_token, purchase_price=None, item_name=None):
        """
        Buy the specified item using SIH API.

        Args:
            steam_id (str): Your Steam ID
            trade_token (str): Your Steam trade token
            purchase_price (float, optional): The price to pay for the item.
            item_name (str, optional): Name of the item to buy. If None, uses the first target item.

        Returns:
            tuple: (success: bool, custom_id: str or None) - True if purchase initiated, custom_id for tracking
        """
        try:
            # Use the provided item name or fall back to the first target item
            target_item = item_name or (self.target_items[0]['name'] if self.target_items else "")

            # Validate required parameters
            if not steam_id or not trade_token:
                logging.error("Steam ID and trade token are required for purchases")
                return False, None

            if not target_item:
                logging.error("Item name is required for purchases")
                return False, None

            # If no purchase price is specified, use the maximum price for this item
            if purchase_price is None:
                # Find the max price for this specific item
                for item in self.target_items:
                    if item['name'] == target_item:
                        actual_price = item['max_price']
                        break
                else:
                    actual_price = 1.0  # Default fallback price
            else:
                actual_price = purchase_price

            # Generate a custom ID based on timestamp and a random component for uniqueness
            custom_id = f"auto_buy_{int(time.time())}_{os.urandom(4).hex()}"

            # Log purchase attempt details
            logging.info(f"Attempting to purchase {target_item} for ${actual_price}")
            logging.info(f"Using Steam ID: {steam_id} and trade token: {trade_token}")

            # Prepare the purchase request
            purchase_data = {
                "steamId": steam_id,
                "token": trade_token,
                "amount": actual_price,
                "item": target_item,
                "customId": custom_id,
                "test": False,  # Set to True for testing without actual purchase
                "appId": self.app_id
            }

            # Create the order
            logging.info(f"Sending purchase request to SIH API...")
            response = requests.post(
                f"{self.base_url}/create-order",
                headers=self.headers,
                json=purchase_data
            )

            # Handle the response
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    order_id = data.get('id', 'Unknown')
                    balance = data.get('balance', 'Unknown')
                    logging.info(f"Purchase successful! Order ID: {order_id}")
                    logging.info(f"Remaining balance: ${balance}")

                    # Return success with custom_id for order tracking
                    return True, custom_id
                else:
                    error = data.get('error', 'Unknown error')
                    logging.error(f"Purchase failed: {error}")

                    # Handle specific error cases
                    if "insufficient balance" in error.lower():
                        logging.error(f"Your SIH balance is too low. Please add funds to your account.")
                    elif "invalid tradelink" in error.lower():
                        logging.error(f"The trade token is invalid. Please check your Steam trade URL.")
                    elif "steam guard" in error.lower():
                        logging.error(f"Steam Guard issue: {error}")

            elif response.status_code == 409:
                # Handle duplicate custom ID
                data = response.json()
                if data.get("error") == "custom id already exists":
                    logging.warning("Duplicate custom ID. This purchase was already attempted.")
                    order_info = data.get("order", {})
                    order_status = order_info.get("status", "unknown")
                    logging.info(f"Existing order status: {order_status}")

                    # If the order is already in progress, consider it a success
                    if order_status in ["created", "processing", "sent"]:
                        return True, custom_id
                else:
                    logging.error(f"Purchase conflict: {data.get('error', 'Unknown error')}")
            else:
                logging.error(f"Purchase request failed with status code {response.status_code}")
                logging.error(f"Response: {response.text}")

            return False, None
        except Exception as e:
            logging.error(f"Error buying item: {str(e)}")
            return False, None



    def _play_alert_sound(self):
        """Play a sound alert when an item is purchased."""
        if not self.sound_alert:
            return

        try:
            # Unfortunately, winsound.Beep doesn't support direct volume control
            # For extremely low volume (approximately 1%), we'll use:
            # - Much lower frequencies (barely audible)
            # - Much shorter durations
            # - Minimal number of beeps
            winsound.Beep(300, 100)  # Very low frequency and very short duration for minimal sound
            logging.info("Sound alert played (volume at ~1%)")
        except Exception as e:
            logging.error(f"Failed to play sound alert: {str(e)}")

    def run(self, steam_id, trade_token):
        """
        Run the auto-buyer continuously for multiple items.

        Args:
            steam_id (str): Your Steam ID
            trade_token (str): Your Steam trade token
        """
        logging.info(f"SIH Multi-Item Auto Buyer started")
        logging.info(f"Target items:")
        for item in self.target_items:
            logging.info(f"  - {item['name']}: max ${item['max_price']}, quantity {item['quantity']}")
        logging.info(f"API check interval: {self.api_check_interval} seconds")
        logging.info(f"Price check interval: {self.base_check_interval} seconds (max when adaptive)")
        logging.info(f"Steam ID: {steam_id}")
        logging.info(f"Trade token: {trade_token}")

        # Track consecutive errors to implement exponential backoff
        consecutive_errors = 0
        max_backoff = 3600  # Maximum backoff of 1 hour

        # Track successful purchases to avoid buying too many items
        purchases_today = 0
        purchase_limit = 500  # Increased limit for multiple items
        purchase_day = datetime.now().day

        # Check if we have enough balance before starting
        if not self._check_api_key():
            logging.error("API key validation failed. Exiting.")
            return

        if self.project_info and 'balance' in self.project_info:
            balance = float(self.project_info['balance'])
            total_max_cost = sum(item['max_price'] * item['quantity'] for item in self.target_items)
            logging.info(f"Total maximum cost for all items: ${total_max_cost}")

            if balance < min(item['max_price'] for item in self.target_items):
                logging.error(f"Insufficient balance (${balance}) to buy even the cheapest item. Exiting.")
                return

        while True:
            try:
                # Reset purchase counter if it's a new day
                current_day = datetime.now().day
                if current_day != purchase_day:
                    purchases_today = 0
                    purchase_day = current_day
                    logging.info(f"New day started. Purchase counter reset.")

                # Check if we've reached the daily purchase limit
                if purchases_today >= purchase_limit:
                    logging.warning(f"Daily purchase limit reached ({purchase_limit} items). Waiting until tomorrow.")
                    # Sleep until midnight
                    now = datetime.now()
                    tomorrow = datetime(now.year, now.month, now.day) + timedelta(days=1)
                    seconds_until_tomorrow = (tomorrow - now).total_seconds()
                    time.sleep(min(seconds_until_tomorrow, 3600))  # Sleep at most 1 hour
                    continue

                # Check if API key is working
                api_working = self._check_api_key()

                # If API key is not working, wait and check again
                if not api_working:
                    logging.info(f"Waiting for API key to become valid...")
                    time.sleep(self.api_check_interval)
                    continue

                # Check if all items have been bought
                all_items_bought = all(item['bought'] >= item['quantity'] for item in self.target_items)
                if all_items_bought:
                    logging.info("All target items have been purchased! Auto-buyer completed successfully.")
                    return

                # Check prices for all items
                items_to_buy = []
                for i, item in enumerate(self.target_items):
                    if item['bought'] >= item['quantity']:
                        continue  # Skip items that are already fully bought

                    # Add delay between API calls to prevent rate limiting
                    if i > 0:  # Don't delay before first item
                        time.sleep(1.0)  # 1 second delay between requests

                    logging.debug(f"Checking price for item {i+1}/{len(self.target_items)}: {item['name']}")
                    current_price, available_count = self.get_item_price(item['name'])

                    if current_price is None:
                        logging.warning(f"Could not get price for {item['name']} (item {i+1}/{len(self.target_items)}). Skipping this item for now.")
                        # Increment consecutive errors for adaptive interval
                        self.consecutive_errors += 1

                        # If we're getting too many consecutive errors, add a longer delay
                        if self.consecutive_errors >= 3:
                            logging.warning(f"Multiple consecutive API errors ({self.consecutive_errors}). Adding 2-second delay to prevent rate limiting.")
                            time.sleep(2)

                        continue

                    logging.info(f"{item['name']}: ${current_price} (Available: {available_count}) - Target: ${item['max_price']} - Progress: {item['bought']}/{item['quantity']}")

                    if current_price <= item['max_price'] and available_count > 0:
                        remaining_to_buy = item['quantity'] - item['bought']
                        items_to_buy.append({
                            'item': item,
                            'price': current_price,
                            'available': available_count,
                            'remaining': remaining_to_buy
                        })

                # Reset consecutive errors on successful price fetch
                consecutive_errors = 0

                # Process purchases for items that are below threshold
                if items_to_buy:
                    logging.info(f"Found {len(items_to_buy)} item(s) below threshold price!")

                    for purchase in items_to_buy:
                        item = purchase['item']
                        price = purchase['price']
                        available = purchase['available']
                        remaining = purchase['remaining']

                        # Check if we should skip this purchase based on recent purchase history
                        should_skip, reason = self._should_skip_purchase(item['name'], price)
                        if should_skip:
                            logging.info(f"Skipping {item['name']} - {reason}")
                            continue

                        # Calculate how many we can buy with current balance
                        if self.project_info and 'balance' in self.project_info:
                            current_balance = float(self.project_info['balance'])
                            can_buy_count = min(int(current_balance / price), remaining, available)

                            if can_buy_count <= 0:
                                logging.warning(f"Insufficient balance (${current_balance}) to buy {item['name']} at ${price}. Skipping.")
                                continue
                        else:
                            can_buy_count = min(1, remaining, available)  # Default to buying one

                        # Smart purchase system will prevent buying the same exact item multiple times
                        # We can safely attempt to buy multiple items if they're at different price points
                        max_per_cycle = min(can_buy_count, 3)  # Limit to 3 per cycle for reasonable batch size

                        logging.info(f"Attempting to buy {max_per_cycle} {item['name']}(s) at ${price} each")

                        # Try to buy the items
                        for i in range(max_per_cycle):
                            success, custom_id = self.buy_item(steam_id, trade_token, price, item['name'])

                            if success:
                                logging.info(f"Purchase {i+1}/{max_per_cycle} of {item['name']} initiated successfully at ${price}!")
                                # Play sound alert for successful purchase
                                self._play_alert_sound()
                                # Increment purchase counters
                                purchases_today += 1
                                item['bought'] += 1
                                self.last_purchase_time = datetime.now()  # Track purchase time for adaptive interval

                                # Track this purchase with price information for smart duplicate prevention
                                self.recent_purchases[item['name']] = {
                                    'price': price,
                                    'time': datetime.now(),
                                    'count': self.recent_purchases.get(item['name'], {}).get('count', 0) + 1
                                }
                                logging.info(f"Tracked purchase: {item['name']} at ${price} (purchase #{self.recent_purchases[item['name']]['count']})")

                                # Add order to pending verification queue
                                if custom_id:
                                    self.pending_orders[custom_id] = {
                                        'item_name': item['name'],
                                        'price': price,
                                        'time': datetime.now(),
                                        'verified': False
                                    }
                                    logging.info(f"Added order {custom_id} to verification queue")

                                logging.info(f"Purchases today: {purchases_today}/{purchase_limit}")
                                logging.info(f"Progress for {item['name']}: {item['bought']}/{item['quantity']} items bought")

                                # Short delay between purchases
                                time.sleep(2)

                                # Update project info to get current balance
                                self._check_api_key()

                                # Continue buying more if available - smart tracking will prevent duplicates
                            else:
                                logging.warning(f"Purchase {i+1}/{max_per_cycle} of {item['name']} failed. Stopping batch purchase for this item.")
                                break
                else:
                    logging.info("No items are currently below their threshold prices.")

                # Verify pending orders to check if purchases were successful
                self._verify_pending_orders()

                # Calculate adaptive interval based on market conditions
                # Count items that were close to threshold in this cycle (avoid extra API calls)
                items_near_threshold = len(items_to_buy)  # Items that are already below threshold

                # Also count items that are close but not quite there yet
                # (We already have price data from the previous loop, so we can reuse it)

                recent_purchases = (self.last_purchase_time and
                                  (datetime.now() - self.last_purchase_time).total_seconds() < 60)

                # Calculate next interval
                self.current_interval = self._calculate_adaptive_interval(
                    items_near_threshold=items_near_threshold,
                    recent_purchases=recent_purchases,
                    api_errors=self.consecutive_errors
                )

                # Display a loading bar during the wait interval
                if self.adaptive_interval:
                    logging.info(f"Adaptive interval: {self.current_interval}s (items near threshold: {items_near_threshold}, recent purchases: {recent_purchases})")
                else:
                    logging.info(f"Waiting {self.current_interval} seconds before next check...")

                # Check if progress bar is enabled
                if self.show_progress_bar:
                    wait_with_progress_bar(
                        self.current_interval,
                        description=f"Checking prices again in"
                    )
                else:
                    # Simple waiting without progress bar
                    time.sleep(self.current_interval)

            except KeyboardInterrupt:
                logging.info("Auto-buyer stopped by user.")
                break
            except Exception as e:
                logging.error(f"Error in auto-buyer loop: {str(e)}")
                # Increment consecutive errors counter
                consecutive_errors += 1

                # Calculate backoff time with exponential increase
                backoff_time = min(self.check_interval * (2 ** consecutive_errors), max_backoff)
                logging.warning(f"Error occurred. Backing off for {backoff_time} seconds.")
                time.sleep(backoff_time)


if __name__ == "__main__":
    import argparse

    # Set up command line arguments
    parser = argparse.ArgumentParser(description='SIH Auto Buyer - Automatically buy items when price drops below threshold')
    parser.add_argument('--interval', type=int, default=120, help='Check interval in seconds (default: 120)')
    parser.add_argument('--steam-id', type=str, help='Your Steam ID')
    parser.add_argument('--trade-token', type=str, help='Your Steam trade token')
    parser.add_argument('--max-price', type=float, default=1.00, help='Maximum price to pay (default: 1.00)')
    parser.add_argument('--quantity', type=int, default=10, help='Number of items to buy (default: 10)')
    parser.add_argument('--no-sound', action='store_true', help='Disable sound alerts on purchase')
    parser.add_argument('--no-progress-bar', action='store_true', help='Disable the progress bar display')
    parser.add_argument('--no-adaptive-interval', action='store_true', help='Disable adaptive interval (use fixed interval)')
    parser.add_argument('--api-check-interval', type=int, default=300, help='How often to check if API key is working in seconds (default: 300)')
    parser.add_argument('--api-key', type=str, help='Your SIH API key (overrides the default)')
    parser.add_argument('--item', type=str, help='The item to buy (overrides the default)')
    parser.add_argument('--app-id', type=int, default=730, help='Steam app ID (default: 730 for CS:GO)')
    parser.add_argument('--log-file', type=str, default='sih_autobuy.log', help='Log file path (default: sih_autobuy.log)')
    args = parser.parse_args()

    # Configure logging with custom log file
    if args.log_file != 'sih_autobuy.log':
        # Reconfigure logging with the custom log file
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(args.log_file),
                logging.StreamHandler()
            ]
        )
        logging.info(f"Logging to {args.log_file}")

    # Configuration
    API_KEY = args.api_key or "iehs6hVfrAF69vAipUuSfNI7k7qPorbYDzHowyf9"
    CHECK_INTERVAL = args.interval
    SOUND_ALERT = not args.no_sound
    SHOW_PROGRESS_BAR = not args.no_progress_bar
    ADAPTIVE_INTERVAL = not args.no_adaptive_interval
    API_CHECK_INTERVAL = args.api_check_interval
    APP_ID = args.app_id

    # Steam ID and trade token
    STEAM_ID = args.steam_id or "76561198028787403"  # Pavel's Steam ID
    TRADE_TOKEN = args.trade_token or "bAhEDBx_"  # Pavel's trade token

    # Define the target items with their maximum prices and quantities
    TARGET_ITEMS = [
        {"name": "Nova | Candy Apple (Factory New)", "max_price": 0.333, "quantity": 5, "bought": 0},
        {"name": "AWP | Safari Mesh (Field-Tested)", "max_price": 0.111, "quantity": 5, "bought": 0},
        {"name": "MAC-10 | Candy Apple (Factory New)", "max_price": 0.6, "quantity": 5, "bought": 0},
        {"name": "Glock-18 | Candy Apple (Factory New)", "max_price": 2.4, "quantity": 1, "bought": 0},
        {"name": "Dreams & Nightmares Case", "max_price": 0.66, "quantity": 5, "bought": 0},
        {"name": "Fracture Case", "max_price": 0.28, "quantity": 20, "bought": 0},
        {"name": "Danger Zone Case", "max_price": 1.35, "quantity": 5, "bought": 0},
        {"name": "Recoil Case", "max_price": 0.20, "quantity": 5, "bought": 0},
        {"name": "Kilowatt Case", "max_price": 0.31, "quantity": 5, "bought": 0},
        {"name": "Revolution Case", "max_price": 0.40, "quantity": 5, "bought": 0},
        {"name": "Clutch Case", "max_price": 0.51, "quantity": 5, "bought": 0},
        {"name": "CS:GO Weapon Case", "max_price": 1.11, "quantity": 5, "bought": 0},
        {"name": "Shadow Case", "max_price": 1.11, "quantity": 5, "bought": 0},
        {"name": "Falchion Case", "max_price": 1.15, "quantity": 5, "bought": 0},
        {"name": "Prisma Case", "max_price": 1.11, "quantity": 5, "bought": 0}
         
    ]

    # Log configuration
    logging.info(f"Configuration:")
    logging.info(f"- API Key: {API_KEY[:5]}...{API_KEY[-5:]} (masked for security)")
    logging.info(f"- Target Items:")
    for item in TARGET_ITEMS:
        logging.info(f"  * {item['name']}: max ${item['max_price']}, quantity {item['quantity']}")
    logging.info(f"- Check Interval: {CHECK_INTERVAL} seconds {'(max when adaptive)' if ADAPTIVE_INTERVAL else '(fixed)'}")
    logging.info(f"- Adaptive Interval: {'Enabled (5-{} seconds)'.format(CHECK_INTERVAL) if ADAPTIVE_INTERVAL else 'Disabled'}")
    logging.info(f"- API Check Interval: {API_CHECK_INTERVAL} seconds")
    logging.info(f"- App ID: {APP_ID}")
    logging.info(f"- Sound Alerts: {'Disabled' if not SOUND_ALERT else 'Enabled (1% volume)'}")
    logging.info(f"- Progress Bar: {'Disabled' if not SHOW_PROGRESS_BAR else 'Enabled'}")

    # Create and run the auto-buyer
    auto_buyer = SIHAutoBuyer(API_KEY,
                             target_items=TARGET_ITEMS,
                             check_interval=CHECK_INTERVAL,
                             app_id=APP_ID,
                             sound_alert=SOUND_ALERT,
                             api_check_interval=API_CHECK_INTERVAL,
                             show_progress_bar=SHOW_PROGRESS_BAR,
                             adaptive_interval=ADAPTIVE_INTERVAL)
    auto_buyer.run(STEAM_ID, TRADE_TOKEN)
