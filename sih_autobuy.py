import requests
import time
import logging
import os
import json
import sys
import winsound  # For Windows sound alerts
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("sih_autobuy.log"),
        logging.StreamHandler()
    ]
)

def wait_with_progress_bar(seconds, description="Waiting"):
    """
    Wait for the specified number of seconds with a very simple progress bar.

    Args:
        seconds (int): Number of seconds to wait
        description (str): Description to show in the progress bar
    """
    try:
        # Create a very simple progress bar that stays on one line
        for i in range(seconds, 0, -1):
            # Use a simple countdown format
            sys.stdout.write(f'\r{description} {i} seconds remaining...   ')
            sys.stdout.flush()
            time.sleep(1)

        # Complete the progress bar
        sys.stdout.write(f'\r{description} complete!                      \n')
        sys.stdout.flush()
    except Exception as e:
        # If there's an error with the progress bar, fall back to simple waiting
        logging.warning(f"Progress bar error: {str(e)}. Falling back to simple waiting.")
        logging.info(f"Waiting for {seconds} seconds...")
        time.sleep(seconds)

class SIHAutoBuyer:
    def __init__(self, api_key, target_item, max_price, check_interval=120, app_id=730, sound_alert=True,
                 api_check_interval=300, quantity_to_buy=2, show_progress_bar=True):
        """
        Initialize the SIH Auto Buyer.

        Args:
            api_key (str): Your SIH API key
            target_item (str): The name of the item to buy
            max_price (float): Maximum price to pay for the item in USD
            check_interval (int): How often to check prices in seconds
            app_id (int): The Steam app ID (730 for CS:GO)
            sound_alert (bool): If True, play a sound alert when an item is purchased
            api_check_interval (int): How often to check if API key is working (in seconds)
            quantity_to_buy (int): How many items to buy in total
            show_progress_bar (bool): If True, display a progress bar during wait intervals
        """
        self.api_key = api_key
        self.target_item = target_item
        self.max_price = max_price
        self.check_interval = check_interval
        self.app_id = app_id
        self.sound_alert = sound_alert
        self.api_check_interval = api_check_interval
        self.quantity_to_buy = quantity_to_buy
        self.quantity_bought = 0  # Track how many items have been bought
        self.show_progress_bar = show_progress_bar
        self.last_api_check = datetime.now() - timedelta(seconds=api_check_interval)  # Force immediate check
        self.base_url = "https://api.sih.market/api/v1"
        self.headers = {
            "apikey": self.api_key
        }
        self.project_info = None

    def _check_api_key(self):
        """Check if the API key is working and update test_mode accordingly."""
        # Check if it's time to check the API key
        if (datetime.now() - self.last_api_check).total_seconds() < self.api_check_interval:
            # Not time to check API key yet
            return True  # Assume API key is still valid if we checked recently

        self.last_api_check = datetime.now()

        # Check if the API key is working
        logging.info(f"Checking if API key is working...")

        # Try up to 3 times to get project info
        max_retries = 3
        for attempt in range(1, max_retries + 1):
            try:
                self.project_info = self._get_project_info()

                if self.project_info:
                    logging.info(f"API key is working! Connected to SIH project: {self.project_info['name']}")
                    logging.info(f"Current balance: ${self.project_info['balance']}")
                    return True
                else:
                    if attempt < max_retries:
                        logging.warning(f"API key validation failed (attempt {attempt}/{max_retries}). Retrying...")
                        time.sleep(2)  # Short delay before retry
                    else:
                        logging.warning(f"API key is not working after {max_retries} attempts. Will check again in {self.api_check_interval} seconds.")
            except Exception as e:
                if attempt < max_retries:
                    logging.warning(f"Error checking API key (attempt {attempt}/{max_retries}): {str(e)}. Retrying...")
                    time.sleep(2)  # Short delay before retry
                else:
                    logging.error(f"Error checking API key after {max_retries} attempts: {str(e)}")

        return False

    def _get_project_info(self):
        """Get information about the current project."""
        try:
            response = requests.get(
                f"{self.base_url}/project",
                headers=self.headers
            )
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    return data.get("project")
            logging.error(f"Failed to get project info: {response.text}")
            return None
        except Exception as e:
            logging.error(f"Error getting project info: {str(e)}")
            return None

    def get_item_price(self):
        """
        Get the current price of the target item using SIH API.

        Returns:
            tuple: (price, count) if successful, (None, 0) otherwise
        """
        try:
            # First try to get the minimum price for the specific item
            response = requests.get(
                f"{self.base_url}/get-min-item",
                headers=self.headers,
                params={
                    "item": self.target_item,
                    "appId": self.app_id
                }
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("success") and data.get("items"):
                    item_data = data["items"].get(self.target_item)
                    if item_data:
                        price = item_data.get("price")
                        count = item_data.get("count", 0)

                        # Log additional item details if available
                        market = item_data.get("market", "Unknown")
                        sell_price = item_data.get("sell", "N/A")
                        steam_price = item_data.get("steam", "N/A")

                        # Get detailed price information if available
                        detailed_prices = []
                        if "detailed" in item_data:
                            detailed = item_data.get("detailed", [])
                            for price_info in detailed[:5]:  # Show up to 5 lowest prices
                                price_val = price_info.get("price", "N/A")
                                count_val = price_info.get("count", 0)
                                detailed_prices.append(f"${price_val} (x{count_val})")

                            if detailed_prices:
                                logging.info(f"Available prices: {', '.join(detailed_prices)}")

                        logging.info(f"Item details - Market: {market}, Sell price: ${sell_price}, Steam price: ${steam_price}")
                        return price, count
                    else:
                        logging.warning(f"Item '{self.target_item}' not found in response")
                else:
                    error_msg = data.get("error", "Unknown error")
                    logging.error(f"API returned error: {error_msg}")
            else:
                logging.error(f"Failed to get item price. Status code: {response.status_code}")
                logging.error(f"Response: {response.text}")

            # If we couldn't get the specific item price, try getting all items
            # This might help diagnose if the item name is correct
            try:
                logging.info("Attempting to get all available items...")
                all_items_response = requests.get(
                    f"{self.base_url}/get-items",
                    headers=self.headers,
                    params={"appId": self.app_id, "minified": True}
                )

                if all_items_response.status_code == 200:
                    all_items_data = all_items_response.json()
                    if all_items_data.get("success") and all_items_data.get("items"):
                        item_count = len(all_items_data["items"])
                        logging.info(f"Found {item_count} items available on SIH market")

                        # Check if our target item is in the list with a different name
                        similar_items = [item for item in all_items_data["items"].keys()
                                        if self.target_item.lower() in item.lower()]

                        if similar_items:
                            logging.info(f"Found similar items: {', '.join(similar_items[:5])}")
                            logging.info(f"Consider using one of these exact names instead")
                    else:
                        logging.warning("Could not retrieve item list")
                else:
                    logging.warning(f"Failed to get all items. Status: {all_items_response.status_code}")
            except Exception as e:
                logging.error(f"Error while trying to get all items: {str(e)}")

            return None, 0
        except Exception as e:
            logging.error(f"Error getting item price: {str(e)}")
            return None, 0

    def buy_item(self, steam_id, trade_token, purchase_price=None):
        """
        Buy the target item using SIH API.

        Args:
            steam_id (str): Your Steam ID
            trade_token (str): Your Steam trade token
            purchase_price (float, optional): The price to pay for the item.
                                             If None, uses the maximum price.

        Returns:
            bool: True if purchase was successful, False otherwise
        """
        try:
            # If no purchase price is specified, use the maximum price
            actual_price = purchase_price if purchase_price is not None else self.max_price

            # Generate a custom ID based on timestamp and a random component for uniqueness
            custom_id = f"auto_buy_{int(time.time())}_{os.urandom(4).hex()}"

            # Log purchase attempt details
            logging.info(f"Attempting to purchase {self.target_item} for ${actual_price}")
            logging.info(f"Using Steam ID: {steam_id} and trade token: {trade_token}")

            # Prepare the purchase request
            purchase_data = {
                "steamId": steam_id,
                "token": trade_token,
                "amount": actual_price,
                "item": self.target_item,
                "customId": custom_id,
                "test": False,  # Set to True for testing without actual purchase
                "appId": self.app_id
            }

            # Create the order
            logging.info(f"Sending purchase request to SIH API...")
            response = requests.post(
                f"{self.base_url}/create-order",
                headers=self.headers,
                json=purchase_data
            )

            # Handle the response
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    order_id = data.get('id', 'Unknown')
                    balance = data.get('balance', 'Unknown')
                    logging.info(f"Purchase successful! Order ID: {order_id}")
                    logging.info(f"Remaining balance: ${balance}")

                    # Check order status
                    self._check_order_status(order_id)
                    return True
                else:
                    error = data.get('error', 'Unknown error')
                    logging.error(f"Purchase failed: {error}")

                    # Handle specific error cases
                    if "insufficient balance" in error.lower():
                        logging.error(f"Your SIH balance is too low. Please add funds to your account.")
                    elif "invalid tradelink" in error.lower():
                        logging.error(f"The trade token is invalid. Please check your Steam trade URL.")
                    elif "steam guard" in error.lower():
                        logging.error(f"Steam Guard issue: {error}")

            elif response.status_code == 409:
                # Handle duplicate custom ID
                data = response.json()
                if data.get("error") == "custom id already exists":
                    logging.warning("Duplicate custom ID. This purchase was already attempted.")
                    order_info = data.get("order", {})
                    order_status = order_info.get("status", "unknown")
                    logging.info(f"Existing order status: {order_status}")

                    # If the order is already in progress, consider it a success
                    if order_status in ["created", "processing", "sent"]:
                        return True
                else:
                    logging.error(f"Purchase conflict: {data.get('error', 'Unknown error')}")
            else:
                logging.error(f"Purchase request failed with status code {response.status_code}")
                logging.error(f"Response: {response.text}")

            return False
        except Exception as e:
            logging.error(f"Error buying item: {str(e)}")
            return False

    def _check_order_status(self, order_id):
        """
        Check the status of an order.

        Args:
            order_id: The ID of the order to check
        """
        try:
            logging.info(f"Checking status of order {order_id}...")
            response = requests.get(
                f"{self.base_url}/get-order",
                headers=self.headers,
                params={"id": order_id}
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("success") and data.get("order"):
                    order = data["order"]
                    status = order.get("status", "unknown")
                    item = order.get("item", "unknown")
                    amount = order.get("amount", 0)
                    expected_amount = order.get("expectedAmount", 0)

                    logging.info(f"Order status: {status}")
                    logging.info(f"Item: {item}, Amount: ${amount}, Expected: ${expected_amount}")

                    if status == "created":
                        logging.info("Order created and awaiting item search")
                    elif status == "processing":
                        logging.info("Item found, waiting for seller to send trade offer")
                    elif status == "sent":
                        logging.info("Item was sent by seller, waiting for you to accept the trade offer")

                        # Log sender information if available
                        sender = order.get("sender", {})
                        if sender:
                            offer_id = sender.get("offerId", "Unknown")
                            nickname = sender.get("nickname", "Unknown")
                            timeout = sender.get("timeout", 0)
                            if timeout:
                                timeout_time = datetime.fromtimestamp(timeout)
                                logging.info(f"Trade offer from {nickname} (ID: {offer_id})")
                                logging.info(f"Offer expires at {timeout_time}")
                    elif status == "finished":
                        logging.info("Purchase completed successfully!")
                    elif status in ["failed", "penalized"]:
                        error = order.get("error", "Unknown error")
                        logging.error(f"Purchase {status}: {error}")
                else:
                    logging.warning(f"Could not get order info: {data.get('error', 'Unknown error')}")
            else:
                logging.error(f"Failed to check order status. Response: {response.text}")
        except Exception as e:
            logging.error(f"Error checking order status: {str(e)}")

    def _play_alert_sound(self):
        """Play a sound alert when an item is purchased."""
        if not self.sound_alert:
            return

        try:
            # Unfortunately, winsound.Beep doesn't support direct volume control
            # For extremely low volume (approximately 1%), we'll use:
            # - Much lower frequencies (barely audible)
            # - Much shorter durations
            # - Minimal number of beeps
            winsound.Beep(300, 100)  # Very low frequency and very short duration for minimal sound
            logging.info("Sound alert played (volume at ~1%)")
        except Exception as e:
            logging.error(f"Failed to play sound alert: {str(e)}")

    def run(self, steam_id, trade_token):
        """
        Run the auto-buyer continuously.

        Args:
            steam_id (str): Your Steam ID
            trade_token (str): Your Steam trade token
        """
        logging.info(f"SIH Auto Buyer started")
        logging.info(f"Target item: {self.target_item}")
        logging.info(f"Maximum price: ${self.max_price}")
        logging.info(f"Quantity to buy: {self.quantity_to_buy}")
        logging.info(f"API check interval: {self.api_check_interval} seconds")
        logging.info(f"Price check interval: {self.check_interval} seconds")
        logging.info(f"Steam ID: {steam_id}")
        logging.info(f"Trade token: {trade_token}")

        # Track consecutive errors to implement exponential backoff
        consecutive_errors = 0
        max_backoff = 3600  # Maximum backoff of 1 hour

        # Track successful purchases to avoid buying too many items
        purchases_today = 0
        purchase_limit = 10  # Maximum purchases per day
        purchase_day = datetime.now().day

        # Reset quantity bought counter
        self.quantity_bought = 0

        # Check if we have enough balance before starting
        if not self._check_api_key():
            logging.error("API key validation failed. Exiting.")
            return

        if self.project_info and 'balance' in self.project_info:
            balance = float(self.project_info['balance'])
            if balance < self.max_price:
                logging.error(f"Insufficient balance (${balance}) to buy even one item at max price ${self.max_price}. Exiting.")
                return

            # Calculate how many items we can buy with current balance
            max_possible_items = int(balance / self.max_price)
            logging.info(f"Current balance: ${balance} - Can buy up to {max_possible_items} items at max price ${self.max_price}")

            # Adjust quantity to buy based on available balance
            if max_possible_items < self.quantity_to_buy:
                logging.warning(f"Not enough balance to buy {self.quantity_to_buy} items. Will try to buy {max_possible_items} items instead.")
                self.quantity_to_buy = max_possible_items

            if self.quantity_to_buy == 0:
                logging.error("Cannot buy any items with current balance. Exiting.")
                return

        while True:
            try:
                # Reset purchase counter if it's a new day
                current_day = datetime.now().day
                if current_day != purchase_day:
                    purchases_today = 0
                    purchase_day = current_day
                    logging.info(f"New day started. Purchase counter reset.")

                # Check if we've reached the daily purchase limit
                if purchases_today >= purchase_limit:
                    logging.warning(f"Daily purchase limit reached ({purchase_limit} items). Waiting until tomorrow.")
                    # Sleep until midnight
                    now = datetime.now()
                    tomorrow = datetime(now.year, now.month, now.day) + timedelta(days=1)
                    seconds_until_tomorrow = (tomorrow - now).total_seconds()
                    time.sleep(min(seconds_until_tomorrow, 3600))  # Sleep at most 1 hour
                    continue

                # Check if API key is working
                api_working = self._check_api_key()

                # If API key is not working, wait and check again
                if not api_working:
                    logging.info(f"Waiting for API key to become valid...")
                    time.sleep(self.api_check_interval)
                    continue

                # API key is working, proceed with price check and purchase
                current_price, available_count = self.get_item_price()

                if current_price is not None:
                    # Reset consecutive errors counter on successful API call
                    consecutive_errors = 0

                    logging.info(f"Current price for {self.target_item}: ${current_price} (Available: {available_count})")

                    # Check if we've already bought the desired quantity
                    if self.quantity_bought >= self.quantity_to_buy:
                        logging.info(f"Target quantity reached! Bought {self.quantity_bought}/{self.quantity_to_buy} items.")
                        logging.info(f"Auto-buyer completed successfully.")
                        return  # Exit the method, ending the auto-buyer

                    if current_price <= self.max_price and available_count > 0:
                        logging.info(f"Price is below threshold! Current price: ${current_price}, Max price: ${self.max_price}")

                        # Always use the current (lowest) price instead of the max price for the purchase
                        actual_purchase_price = current_price

                        # Calculate how many items we still need to buy
                        remaining_to_buy = self.quantity_to_buy - self.quantity_bought

                        # Calculate how many we can buy with current balance
                        if self.project_info and 'balance' in self.project_info:
                            current_balance = float(self.project_info['balance'])
                            can_buy_count = min(int(current_balance / actual_purchase_price), remaining_to_buy)

                            if can_buy_count <= 0:
                                logging.error(f"Insufficient balance (${current_balance}) to buy more items at ${actual_purchase_price}. Exiting.")
                                return
                        else:
                            can_buy_count = 1  # Default to buying one if we can't determine balance

                        # Limit by available inventory
                        can_buy_count = min(can_buy_count, available_count)

                        logging.info(f"Attempting to buy {can_buy_count} {self.target_item}(s) at ${actual_purchase_price} each (lowest available price)")
                        logging.info(f"Progress: {self.quantity_bought}/{self.quantity_to_buy} items bought")

                        # Try to buy all remaining items at once
                        for i in range(can_buy_count):
                            success = self.buy_item(steam_id, trade_token, actual_purchase_price)

                            if success:
                                logging.info(f"Purchase {i+1}/{can_buy_count} initiated successfully at ${actual_purchase_price}!")
                                # Play sound alert for successful purchase
                                self._play_alert_sound()
                                # Increment purchase counters
                                purchases_today += 1
                                self.quantity_bought += 1
                                logging.info(f"Purchases today: {purchases_today}/{purchase_limit}")
                                logging.info(f"Progress: {self.quantity_bought}/{self.quantity_to_buy} items bought")

                                # Check if we've reached the target quantity
                                if self.quantity_bought >= self.quantity_to_buy:
                                    logging.info(f"Target quantity reached! Bought {self.quantity_bought}/{self.quantity_to_buy} items.")
                                    logging.info(f"Auto-buyer completed successfully.")
                                    return  # Exit the method, ending the auto-buyer

                                # Short delay between purchases
                                time.sleep(2)
                            else:
                                logging.warning(f"Purchase {i+1}/{can_buy_count} failed. Stopping batch purchase.")
                                break

                        # Update project info to get current balance
                        self._check_api_key()
                    else:
                        if current_price > self.max_price:
                            logging.info(f"Price is above threshold (${self.max_price}). Waiting for lower price...")
                        elif available_count <= 0:
                            logging.info("No items available. Waiting for more inventory...")
                else:
                    # Increment consecutive errors counter
                    consecutive_errors += 1

                    # Calculate backoff time with exponential increase
                    backoff_time = min(self.check_interval * (2 ** consecutive_errors), max_backoff)
                    logging.warning(f"Failed to get price. Backing off for {backoff_time} seconds.")
                    time.sleep(backoff_time)
                    continue

                # Display a loading bar during the wait interval
                if self.quantity_bought < self.quantity_to_buy:
                    logging.info(f"Waiting {self.check_interval} seconds before next check...")

                    # Check if progress bar is enabled
                    if self.show_progress_bar:
                        wait_with_progress_bar(
                            self.check_interval,
                            description=f"Checking price again in"
                        )
                    else:
                        # Simple waiting without progress bar
                        time.sleep(self.check_interval)

            except KeyboardInterrupt:
                logging.info("Auto-buyer stopped by user.")
                break
            except Exception as e:
                logging.error(f"Error in auto-buyer loop: {str(e)}")
                # Increment consecutive errors counter
                consecutive_errors += 1

                # Calculate backoff time with exponential increase
                backoff_time = min(self.check_interval * (2 ** consecutive_errors), max_backoff)
                logging.warning(f"Error occurred. Backing off for {backoff_time} seconds.")
                time.sleep(backoff_time)


if __name__ == "__main__":
    import argparse

    # Set up command line arguments
    parser = argparse.ArgumentParser(description='SIH Auto Buyer - Automatically buy items when price drops below threshold')
    parser.add_argument('--interval', type=int, default=120, help='Check interval in seconds (default: 120)')
    parser.add_argument('--steam-id', type=str, help='Your Steam ID')
    parser.add_argument('--trade-token', type=str, help='Your Steam trade token')
    parser.add_argument('--max-price', type=float, default=0.25, help='Maximum price to pay (default: 0.25)')
    parser.add_argument('--quantity', type=int, default=2, help='Number of items to buy (default: 2)')
    parser.add_argument('--no-sound', action='store_true', help='Disable sound alerts on purchase')
    parser.add_argument('--no-progress-bar', action='store_true', help='Disable the progress bar display')
    parser.add_argument('--api-check-interval', type=int, default=300, help='How often to check if API key is working in seconds (default: 300)')
    parser.add_argument('--api-key', type=str, help='Your SIH API key (overrides the default)')
    parser.add_argument('--item', type=str, help='The item to buy (overrides the default)')
    parser.add_argument('--app-id', type=int, default=730, help='Steam app ID (default: 730 for CS:GO)')
    parser.add_argument('--log-file', type=str, default='sih_autobuy.log', help='Log file path (default: sih_autobuy.log)')
    args = parser.parse_args()

    # Configure logging with custom log file
    if args.log_file != 'sih_autobuy.log':
        # Reconfigure logging with the custom log file
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(args.log_file),
                logging.StreamHandler()
            ]
        )
        logging.info(f"Logging to {args.log_file}")

    # Configuration
    API_KEY = args.api_key or "iehs6hVfrAF69vAipUuSfNI7k7qPorbYDzHowyf9"
    TARGET_ITEM = args.item or "Fracture Case"
    MAX_PRICE = args.max_price  # Default is now 0.25 from the argument parser
    QUANTITY_TO_BUY = args.quantity  # Default is 2 from the argument parser
    CHECK_INTERVAL = args.interval
    SOUND_ALERT = not args.no_sound
    SHOW_PROGRESS_BAR = not args.no_progress_bar
    API_CHECK_INTERVAL = args.api_check_interval
    APP_ID = args.app_id

    # Steam ID and trade token
    STEAM_ID = args.steam_id or "76561198028787403"  # Pavel's Steam ID
    TRADE_TOKEN = args.trade_token or "bAhEDBx_"  # Pavel's trade token

    # Log configuration
    logging.info(f"Configuration:")
    logging.info(f"- API Key: {API_KEY[:5]}...{API_KEY[-5:]} (masked for security)")
    logging.info(f"- Target Item: {TARGET_ITEM}")
    logging.info(f"- Max Price: ${MAX_PRICE}")
    logging.info(f"- Quantity to Buy: {QUANTITY_TO_BUY}")
    logging.info(f"- Check Interval: {CHECK_INTERVAL} seconds")
    logging.info(f"- API Check Interval: {API_CHECK_INTERVAL} seconds")
    logging.info(f"- App ID: {APP_ID}")
    logging.info(f"- Sound Alerts: {'Disabled' if not SOUND_ALERT else 'Enabled (1% volume)'}")
    logging.info(f"- Progress Bar: {'Disabled' if not SHOW_PROGRESS_BAR else 'Enabled'}")

    # Create and run the auto-buyer
    auto_buyer = SIHAutoBuyer(API_KEY, TARGET_ITEM, MAX_PRICE, CHECK_INTERVAL,
                             app_id=APP_ID,
                             sound_alert=SOUND_ALERT,
                             api_check_interval=API_CHECK_INTERVAL,
                             quantity_to_buy=QUANTITY_TO_BUY,
                             show_progress_bar=SHOW_PROGRESS_BAR)
    auto_buyer.run(STEAM_ID, TRADE_TOKEN)
