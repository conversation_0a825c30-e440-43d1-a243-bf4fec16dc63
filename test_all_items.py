#!/usr/bin/env python3
"""
Test all items from your TARGET_ITEMS list to see which ones work
"""

import requests
import time

def test_all_items():
    API_KEY = "iehs6hVfrAF69vAipUuSfNI7k7qPorbYDzHowyf9"
    BASE_URL = "https://api.sih.market/api/v1"
    APP_ID = 730
    
    headers = {"apikey": API_KEY}
    
    # Your exact TARGET_ITEMS list
    items = [
        "Nova | Candy Apple (Factory New)",
        "AWP | Safari Mesh (Field-Tested)", 
        "MAC-10 | Candy Apple (Factory New)",
        "Glock-18 | Candy Apple (Factory New)",
        "Fever Case",
        "Fracture Case",
        "Danger Zone Case",
        "Recoil Case",
        "Kilowatt Case",
        "Revolution Case",
        "Clutch Case"
    ]
    
    print("Testing all items from your TARGET_ITEMS list...")
    print("=" * 60)
    
    working_items = []
    failed_items = []
    
    for i, item_name in enumerate(items, 1):
        try:
            print(f"[{i:2d}/{len(items)}] Testing: {item_name}")
            
            response = requests.get(
                f"{BASE_URL}/get-min-item",
                headers=headers,
                params={"item": item_name, "appId": APP_ID},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success") and data.get("items"):
                    item_data = data["items"].get(item_name)
                    if item_data:
                        price = item_data.get("price")
                        count = item_data.get("count", 0)
                        print(f"         ✅ SUCCESS: ${price} (Available: {count})")
                        working_items.append(item_name)
                    else:
                        print(f"         ❌ FAILED: Not found in response")
                        failed_items.append(item_name)
                else:
                    error = data.get("error", "Unknown error")
                    print(f"         ❌ FAILED: {error}")
                    failed_items.append(item_name)
            else:
                print(f"         ❌ FAILED: HTTP {response.status_code}")
                failed_items.append(item_name)
                
        except Exception as e:
            print(f"         ❌ FAILED: Exception - {e}")
            failed_items.append(item_name)
        
        # Add delay between requests to prevent rate limiting
        if i < len(items):  # Don't delay after last item
            time.sleep(1)  # 1 second delay
    
    print("\n" + "=" * 60)
    print("SUMMARY:")
    print(f"✅ Working items ({len(working_items)}):")
    for item in working_items:
        print(f"   - {item}")
    
    print(f"\n❌ Failed items ({len(failed_items)}):")
    for item in failed_items:
        print(f"   - {item}")
    
    print(f"\nPattern analysis:")
    print(f"First 4 items: {items[:4]}")
    print(f"Middle items: {items[4:-4] if len(items) > 8 else 'N/A'}")
    print(f"Last 4 items: {items[-4:] if len(items) >= 4 else items}")

if __name__ == "__main__":
    test_all_items()
