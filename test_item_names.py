#!/usr/bin/env python3
"""
Simple script to test SIH API item names and find the correct name for Fever Case
"""

import requests
import json

def test_item_names():
    API_KEY = "iehs6hVfrAF69vAipUuSfNI7k7qPorbYDzHowyf9"
    BASE_URL = "https://api.sih.market/api/v1"
    APP_ID = 730
    
    headers = {"apikey": API_KEY}
    
    # Test different possible names for Fever Case
    possible_names = [
        "Fever Case",
        "Fever Dream Case", 
        "Dreams & Nightmares Case",
        "Operation Riptide Case",
        "Snakebite Case",
        "Fracture Case"
    ]
    
    print("Testing item names with SIH API...")
    print("=" * 50)
    
    for item_name in possible_names:
        try:
            response = requests.get(
                f"{BASE_URL}/get-min-item",
                headers=headers,
                params={"item": item_name, "appId": APP_ID},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success") and data.get("items"):
                    item_data = data["items"].get(item_name)
                    if item_data:
                        price = item_data.get("price")
                        count = item_data.get("count", 0)
                        print(f"✅ '{item_name}': ${price} (Available: {count})")
                    else:
                        print(f"❌ '{item_name}': Not found in response")
                else:
                    error = data.get("error", "Unknown error")
                    print(f"❌ '{item_name}': API error - {error}")
            else:
                print(f"❌ '{item_name}': HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ '{item_name}': Exception - {e}")
    
    print("\n" + "=" * 50)
    print("If you find a working case name above, update your sih_autobuy.py configuration!")

if __name__ == "__main__":
    test_item_names()
